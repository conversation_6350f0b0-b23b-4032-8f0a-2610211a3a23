# prepare_arc_dataset.py

import pandas as pd
from datasets import load_dataset
import json
import fire


def prepare_arc(output_filepath: str = "arc_easy_processed.jsonl"):
    """
    Downloads the ARC-Easy training dataset, processes it by deduplicating
    and formatting choices, and saves it as a JSONL file.

    Args:
        output_filepath (str): The path to save the final JSONL file.
    """
    print("--- Starting ARC Dataset Preparation ---")

    try:
        # 1. Download the ARC-Easy training data
        print("Downloading allenai/ai2_arc (ARC-Easy)...")
        arc_dataset = load_dataset("allenai/ai2_arc", "ARC-Easy", split="train")
        arc_df = arc_dataset.to_pandas()
        print(f"Downloaded {len(arc_df)} rows from the ARC-Easy train split.")

        # 2. Deduplicate based on the 'question' column
        initial_rows = len(arc_df)
        arc_df.drop_duplicates(subset=["question"], inplace=True, keep="first")
        print(
            f"Deduplicated {initial_rows - len(arc_df)} rows based on identical questions."
        )
        print(f"Remaining rows: {len(arc_df)}")

        # 3. Rename columns for consistency
        arc_df_renamed = arc_df.rename(
            columns={
                "id": "arc_id",
                "question": "question",
                "choices": "choices",
                "answerKey": "answerKey",
            }
        )

        # 4. Flatten the 'choices' column into choice_A, choice_B, etc.
        def extract_choices(choices):
            text_choices = choices["text"]
            labels = choices["label"]
            choice_dict = {}

            # Use standard labels if available, otherwise use index
            if not isinstance(labels, list) or not labels:
                labels = [chr(65 + i) for i in range(len(text_choices))]

            for i, label in enumerate(labels):
                if i < len(text_choices):
                    # Map standard A, B, C, D labels, handling cases with more/fewer choices
                    if label == "A" or i == 0:
                        choice_dict["choice_A"] = text_choices[i]
                    elif label == "B" or i == 1:
                        choice_dict["choice_B"] = text_choices[i]
                    elif label == "C" or i == 2:
                        choice_dict["choice_C"] = text_choices[i]
                    elif label == "D" or i == 3:
                        choice_dict["choice_D"] = text_choices[i]
            return pd.Series(choice_dict)

        print("Processing and flattening multiple choice answers...")
        choices_df = arc_df_renamed["choices"].apply(extract_choices)
        arc_final_df = pd.concat(
            [arc_df_renamed.drop(columns=["choices"]), choices_df], axis=1
        )

        # 5. Reorder and select the final columns
        final_columns = [
            "arc_id",
            "question",
            "choice_A",
            "choice_B",
            "choice_C",
            "choice_D",
            "answerKey",
        ]
        for col in final_columns:
            if col not in arc_final_df:
                arc_final_df[col] = None
        arc_final_df = arc_final_df[final_columns]

        # 6. Save the processed data to a JSONL file
        print(f"Saving processed data to '{output_filepath}'...")
        arc_final_df.to_json(output_filepath, orient="records", lines=True)

        print("\n--- Dataset Preparation Complete ---")
        print(f"Final dataset has {len(arc_final_df)} rows.")
        print(f"Output saved to '{output_filepath}'")

    except Exception as e:
        print(f"\nAn error occurred during dataset preparation: {e}")
        print(
            "Please ensure you have the 'datasets' and 'pandas' libraries installed (`pip install datasets pandas fire`)."
        )


if __name__ == "__main__":
    fire.Fire(prepare_arc)
